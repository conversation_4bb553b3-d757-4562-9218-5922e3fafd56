[![Версия](https://img.shields.io/badge/version-1.6.0-blue.svg)](https://github.com/xxphantom/remnawave-installer)
[![Язык](https://img.shields.io/badge/language-Bash-green.svg)]()
[![Поддержка ОС](https://img.shields.io/badge/OS-Ubuntu-orange.svg)]()

[🇺🇸 Read in English](README.md)

Этот Bash-скрипт предназначен для **автоматизированной установки и первоначальной настройки компонентов Remnawave** (Panel, Node, Subscription-Page) и их окружения (Docker, Caddy, UFW). Скрипт упрощает развертывание, помогает изучить результат и научиться разворачивать панель самому вручную.

> [!CAUTION]
> **ВНИМАНИЕ!** Данный скрипт и репозиторий предоставляются как **образовательный пример** для изучения взаимодействия Caddy и панели Remnawave, а также для демонстрации конфигурации Caddy в качестве обратного прокси. **Скрипт не предназначен для использования в производственной среде без полного понимания его действий и конфигураций Remnawave.** Если вы не понимаете, как работает панель управления Remnawave или создаваемые скриптом конфигурации, это ваша ответственность. **ИСПОЛЬЗУЙТЕ НА СВОЙ СТРАХ И РИСК!**

---

## 🔶 Оглавление

- [Что делает этот скрипт?](#-что-делает-этот-скрипт)
- [Ключевые возможности скрипта](#-ключевые-возможности-скрипта)
- [Требования для работы скрипта и установки Remnawave](#-требования-для-работы-скрипта-и-установки-remnawave)
  - [Аппаратные (для Remnawave)](#аппаратные-для-remnawave)
  - [Программные (для сервера и скрипта)](#программные-для-сервера-и-скрипта)
  - [Сетевые](#сетевые)
- [Быстрый старт: Запуск скрипта](#-быстрый-старт-запуск-скрипта)
- [Сценарии установки, поддерживаемые скриптом](#-сценарии-установки-поддерживаемые-скриптом)
  - [Вариант 1: Два сервера (Панель и Нода раздельно)](#вариант-1-два-сервера-панель-и-нода-раздельно)
  - [Вариант 2: Всё в одном (Панель и Нода на одном сервере)](#вариант-2-всё-в-одном-панель-и-нода-на-одном-сервере)
- [Варианты защиты доступа к панели, настраиваемые скриптом](#️-варианты-защиты-доступа-к-панели-настраиваемые-скриптом)
  - [SIMPLE cookie security](#simple-cookie-security)
  - [FULL Caddy security (рекомендуется)](#full-caddy-security-рекомендуется)
- [Использование скрипта: Меню и опции](#️-использование-скрипта-меню-и-опции)
  - [Главное меню](#главное-меню)
  - [Меню установки](#меню-установки)
  - [Домены](#домены)
- [Важная информация, генерируемая скриптом](#-важная-информация-генерируемая-скриптом)
- [Структура директорий, создаваемая скриптом](#-структура-директорий-создаваемая-скриптом)
- [Управление сервисами после установки](#️-управление-сервисами-после-установки)
- [Система обновлений](#-система-обновлений)
- [Интеграция WARP](#-интеграция-warp)
- [Официальные ресурсы Remnawave](#-официальные-ресурсы-remnawave)
- [Замечания и известные проблемы](#️-замечания-и-известные-проблемы)

---

## 🎯 Что делает этот скрипт?

Этот скрипт **не является заменой панели Remnawave**. Он **автоматизирует процесс развертывания** самой панели Remnawave, ее узлов (Node) и необходимых сопутствующих сервисов.

**Ответственность скрипта:**

- Подготовка серверной среды (установка Docker, UFW, зависимостей).
- Загрузка необходимых образов Docker для Remnawave Panel, Node, Subscription Page, Caddy, PostgreSQL, Redis.
- Генерация конфигурационных файлов (`docker-compose.yml`, `.env` для Remnawave, `Caddyfile`).
- Настройка обратного прокси Caddy для доступа к веб-интерфейсу панели и другим сервисам.
- Реализация двух вариантов защиты доступа к панели через Caddy.
- Первоначальная настройка Remnawave: создание администратора, регистрация ноды, создание тестового пользователя и конфигураций VLESS.
- Предоставление удобного меню для управления установкой (переустановка, удаление, перезапуск).
- Сохранение всех сгенерированных учетных данных и URL в локальный файл

**Ответственность панели Remnawave (после установки скриптом):**

- Управление пользователями, подписками, трафиком.
- Управление узлами (нодами) и их конфигурациями.
- Предоставление API для клиентов и узлов.
- Ведение статистики и мониторинг.
- Все остальные функции, присущие панели управления VPN/прокси.

---

## ✨ Ключевые возможности скрипта

- **Интерактивное меню**: Удобный интерфейс для выбора опций на русском или английском языке.
- **Автоматическая установка зависимостей**: Docker, Docker Compose plugin, UFW, `curl`, `jq`, `openssl` и др.
- **Различные режимы установки**:
  - Только Панель Remnawave (с двумя вариантами защиты Caddy).
  - Только Нода Remnawave (для установки на отдельном сервере).
  - All-in-One: Панель + Нода Remnawave на одном сервере (с двумя вариантами защиты Caddy).
- **Настройка безопасности доступа через Caddy**:
  - **SIMPLE cookie**: Доступ к панели через секретный ключ в URL.
  - **FULL Caddy security**: Использование полноценного портала аутентификации Caddy (через `remnawave/caddy-with-auth`).
- **Автоматическая генерация конфигураций**:
  - `.env` для Remnawave Panel и Node.
  - `docker-compose.yml` для всех компонентов.
  - `Caddyfile` для обратного прокси, SSL и выбранного типа безопасности.
  - `Makefile` для простого управления Docker-контейнерами.
- **Первоначальная инициализация Remnawave**: Регистрация админа, ноды, пользователя.
- **Управление установкой**: Перезапуск, полное удаление (с данными), доступ к Remnawave Rescue CLI.
- **Система обновлений**: Комплексная функциональность обновления компонентов Remnawave с умным определением Docker образов.
- **Интеграция WARP**: Простая настройка Cloudflare WARP с возможностью кастомизации доменов.
- **Отображение и сохранение учетных данных**: Все важные данные сохраняются в `/opt/remnawave/credentials.txt`.
- **Управление BBR**: Включение/отключение BBR.
- **Генерация QR-кода**: Для URL подписки при установке в сценарии All-in-One (Панель + Нода)

---

## 📋 Требования для работы скрипта и установки Remnawave

### Аппаратные (для Remnawave)

Скрипт устанавливает Remnawave, поэтому эти требования относятся к компонентам Remnawave.

- **Remnawave Panel**:
  - ОС: Рекомендуется Ubuntu 22.04 (скрипт тестировался на этой версии Ubuntu)
  - RAM: Минимум 2GB, рекомендуется 4GB.
  - CPU: Минимум 2 ядра, рекомендуется 4 ядра.
  - Хранилище: Минимум 20GB.
- **Remnawave Node**:
  - ОС: Рекомендуется Ubuntu или Debian.
  - RAM: Минимум 1GB.
  - CPU: Минимум 1 ядро.

### Программные (для сервера и скрипта)

- **ОС**: Ubuntu (рекомендуется 22.04) или Debian.
- **Root-доступ**: Скрипт должен запускаться с правами `root` (`sudo`).
- **Основные утилиты**: Docker, Docker Compose plugin, UFW, `curl`, `jq`, `openssl` и др. Скрипт попытается установить их.

### Сетевые

- **Домены**:
  - **Домен Панели**: (например, `panel.example.com`) - для доступа к веб-интерфейсу Remnawave.
  - **Домен Подписки**: (например, `sub.example.com`) - для публичной страницы подписки и API.
  - **Домен Selfsteal**: (например, `login.example.com`) - для VLESS REALITY.
  - Все три домена должны быть **уникальными**.
  - **Важно!** DNS-записи для этих доменов должны быть корректно настроены и указывать на IP-адреса ваших серверов **ДО** запуска скрипта.
- **Открытые порты** (скрипт попытается настроить UFW):
  - `80/tcp`, `443/tcp` (для Caddy).
  - Ваш SSH порт (скрипт определит его или использует `22/tcp`).
  - Для панели вместе с Нодой : порт `2222/tcp` будет открыт только для **********/16 (подсеть Docker).
  - Для раздельной установки Ноды: порт `2222/tcp` на сервере Ноды будет открыт только для ip панели.

---

## 🚀 Быстрый старт: Запуск скрипта

1.  **Загрузите и запустите скрипт одной командой от имени root**:

```bash
sudo bash -c "$(curl -sL https://raw.githubusercontent.com/xxphantom/remnawave-installer/refs/heads/main/install.sh)" @ --lang=ru
```

2.  **Следуйте инструкциям на экране**: Скрипт проведет вас через все шаги установки и настройки.

### Расширенное использование с управлением версиями

Скрипт поддерживает дополнительные параметры для контроля версий компонентов:

**Использование dev версии Remnawave:**

```bash
sudo bash -c "$(curl -sL https://raw.githubusercontent.com/xxphantom/remnawave-installer/refs/heads/main/install.sh)" @ --lang=ru --panel-branch=dev
```

**Использование dev версии скрипта и Remnawave:**

```bash
sudo bash -c "$(curl -sL https://raw.githubusercontent.com/xxphantom/remnawave-installer/refs/heads/main/install.sh)" @ --lang=ru --panel-branch=dev --installer-branch=dev
```

**Доступные параметры:**

- `--lang=en|ru` - Язык интерфейса
- `--panel-branch=main|dev` - Версии Docker образов Remnawave backend и node
- `--installer-branch=main|dev` - Ветка скрипта установки (влияет на URL загрузки)

---

## 🔩 Сценарии установки, поддерживаемые скриптом

Скрипт позволяет развернуть Remnawave в двух основных конфигурациях:

### Вариант 1: Два сервера (Панель и Нода раздельно)

Рекомендуется для большей надежности и гибкости.

- **Сервер Панели**: Здесь скрипт устанавливает Remnawave Panel, Subscription-Page, Caddy (для панели и подписок), PostgreSQL, Redis. Remnawave Нода с Xray здесь не размещается.
- **Сервер Ноды**: Здесь скрипт устанавливает Remnawave Node (с Xray) и Caddy для обслуживания домена Selfsteal.
- **DNS**:
  - Домены Панели и Подписок → IP сервера Панели.
  - Домен Selfsteal → IP сервера Ноды.
- **Порядок действий со скриптом**:
  1.  На сервере Панели выберите установку "Panel Only". Скрипт выведет публичный ключ (`SSL_CERT="..."`), который нужно сохранить.
  2.  На сервере Ноды выберите установку "Node only". Скрипт запросит домен selfsteal, ip панели и ранее сохраненный публичный ключ.

### Вариант 2: Всё в одном (Панель и Нода на одном сервере)

Упрощенный вариант, подходит для тестирования или небольших нагрузок.

- **Один сервер**: Скрипт устанавливает Remnawave Panel, Node, Subscription-Page, Caddy, PostgreSQL, Redis.
- **DNS**: Все три домена (разных!) (Панели, Подписок, Selfsteal) → IP этого единственного сервера.
- **Маршрутизация трафика (как настраивает скрипт)**:
  Remnawave Node (Xray) слушает порт `443` и перенаправляет трафик, который не является VLESS-соединением, на локальный порт Caddy (например, `9443`).
  Caddy слушает на локально доступном порту (например, `9443`) и перенаправляет трафик в зависимости от SNI:

  - Запросы с SNI для домена панели перенаправляются на порт `3000` (Remnawave Panel).
  - Запросы с SNI для домена подписок перенаправляются на порт `3010` (Subscription Page).
  - Запросы с SNI для домена Selfsteal перенаправляются на статическую HTML-страницу (Selfsteal).

  ```
  Клиент → 443 порт → Xray (локальная Remnawave Node)
                        ├─ (Прокси-трафик VLESS) → Обрабатывается Xray
                        └─ (Не VLESS-трафик, fallback) → Caddy (слушает на внутреннем порту, например 9443)
                                                          ├─ SNI для Домена Панели → Remnawave Panel (порт 3000)
                                                          ├─ SNI для Домена Подписок → Subscription Page (порт 3010)
                                                          └─ SNI для Домена Selfsteal → Статическая HTML-страница
  ```

  > **Важно**: В этом режиме, если остановить локальную Remnawave Node или напортачить с конфигом Xray, то панель и другие веб-сервисы станут недоступны через доменные имена, так как Caddy не получит трафик. Пока что скрипт не предоставляет опции "Открыть доступ к панели напрямую", но скоро будет добавлено. Пока что это решается ручной правкой Caddyfile (заменить 127.0.0.1 на 0.0.0.0)

---

## 🛡️ Варианты защиты доступа к панели, настраиваемые скриптом

Скрипт предлагает два варианта защиты доступа к веб-интерфейсу Remnawave Panel с помощью Caddy:

### SIMPLE cookie security

- Доступ к панели осуществляется по специальному URL, содержащему секретный ключ (например, `https://panel.example.com/auth/login?caddy=ВАШ_СЕКРЕТНЫЙ_КЛЮЧ`).
- При первом переходе по такой ссылке Caddy устанавливает cookie. Последующие запросы авторизуются по этому cookie.
- Если cookie/параметр отсутствует или неверный, Caddy показывает статическую страницу-заглушку (Selfsteal сайт).
- Скрипт генерирует секретный ключ и URL, сохраняя их в `credentials.txt`.

### FULL Caddy security (рекомендуется)

- Используется специальный образ `remnawave/caddy-with-auth`, включающий модуль `caddy-security` для полноценного портала аутентификации.
- **Двухуровневая аутентификация**:
  1.  **Caddy Auth Portal**: Пользователь сначала входит здесь (логин/пароль генерируются скриптом, MFA настраивается при первом входе).
  2.  **Remnawave Panel**: После успеха на первом уровне, пользователь получает доступ к стандартной странице входа Remnawave (свои логин/пароль, генерируемые скриптом).
- Панель доступна по уникальному, случайно сгенерированному пути (например, `https://panel.example.com/<СЛУЧАЙНЫЙ_ПУТЬ>/auth`).
- Скрипт генерирует все необходимые учетные данные и URL, сохраняя их в `credentials.txt`.

---

## 🛠️ Использование скрипта: Меню и опции

После запуска скрипт отобразит главное меню. Выбор языка осуществляется параметром при запуске (см. [Быстрый старт](#-быстрый-старт-запуск-скрипта)).

![Главное меню](assets/menu-ru.png)

---

### Домены

Скрипт запросит у вас три доменных имени:

- **Домен панели** (используется для `FRONT_END_DOMAIN` в `.env` панели).
- **Домен подписки** (используется для `SUB_PUBLIC_DOMAIN` в `.env` панели).
- **Домен Selfsteal**.

Убедитесь, что DNS A-записи для этих доменов указывают на правильные IP-адреса **до** начала установки.

## 🔑 Важная информация, генерируемая скриптом

После успешной установки скрипт сохраняет всю критически важную информацию (URL для доступа, логины, пароли, секретные ключи) в файл:

- `/opt/remnawave/credentials.txt` (для установок, включающих панель).

**Надежно сохраните этот файл!**

---

## 📂 Структура директорий, создаваемая скриптом

Скрипт организует файлы конфигурации следующим образом:

- `/opt/remnawave/`: Основная директория для панели Remnawave.
  - `.env`, `docker-compose.yml`, `credentials.txt`, `config.json` (временный для Xray).
  - `caddy/`: Конфигурация Caddy для панели (`Caddyfile`, `docker-compose.yml`, `html/`).
  - `subscription-page/`: Конфигурация страницы подписки (`docker-compose.yml`).
  - `node/`: (Только для All-in-One) Конфигурация локальной ноды (`.env`, `docker-compose.yml`).
- `/opt/remnanode/`: Директория для отдельной установки Remnawave Node.
  - `.env`, `docker-compose.yml`.
  - `selfsteal/`: Конфигурация Caddy для Selfsteal на ноде (`Caddyfile`, `docker-compose.yml`, `html/`).

---

## ⚙️ Управление сервисами после установки

В каждой директории, где скрипт создает `docker-compose.yml` (`/opt/remnawave`, `/opt/remnawave/caddy`, `/opt/remnanode` и т.д.), также создается `Makefile`. Вы можете использовать его для управления Docker-сервисами:

```bash
cd /opt/remnawave # или другая соответствующая директория
make start    # Запустить сервисы и показать логи
make stop     # Остановить сервисы
make restart  # Перезапустить сервисы
make logs     # Показать логи запущенных сервисов
```

---

## 🔄 Система обновлений

Умная система обновления компонентов Remnawave с определением Docker образов. Перезапускает только сервисы с фактическими обновлениями, включает автоматическую очистку неиспользуемых образов.

---

## 🌐 Интеграция WARP

Простая интеграция с Cloudflare WARP для направления трафика через сеть Cloudflare. Автоматизированная настройка. Не требует отдельной настройки каждой ноды - конфиг Xray передается на ноды и ядро Xray обеспечивает соединение с Cloudflare.

---

## 🔗 Официальные ресурсы Remnawave

Этот скрипт автоматизирует многие шаги, но сам по себе не дает понимания, что происходит. Поэтому я рекомендую после ознакомления с панелью провести ручную установку, а для этого вам пригодятся официальные ресурсы Remnawave:

- 📖 [**Документация**](https://remna.st) — официальный сайт
- 📰 [**@remnalog**](https://t.me/remnalog) — последние обновления
- 📢 [**Канал**](https://t.me/remnawave) — официальные анонсы и ссылки
- 💬 [**Группа**](https://t.me/+xQs17zMzwCY1NzYy) — чат для обсуждения

## ⚠️ Замечания и известные проблемы

DNS: Крайне важно корректно настроить DNS A-записи для всех доменов до запуска скрипта.

Cloudflare: Для домена Selfsteal не используйте проксирование Cloudflare (оранжевое облако). DNS-запись должна быть "DNS Only" (серое облако). Скрипт попытается это проверить, но вы тоже не подводите.

Docker Hub Rate Limits: При частых установках и у некоторых хостеров вы можете столкнуться с pull rate лимитом для скачивания образов. Скрипт сообщит об этом. Проще всего запустить docker login и авторизоваться.

Чистая установка: Скрипт рекомендуется запускать на "чистом" сервере без постороннего софта (Nginx, Apache), особенно сервисов, занимающих порты 80/443 и т.д.

Firewall: Скрипт настраивает UFW. При использовании другой системы firewall может потребоваться ручная настройка.

---

По всем вопросам, связанным со скриптом, пожалуйста, обращайтесь к [**@xxphantom**](https://t.me/uphantom).
