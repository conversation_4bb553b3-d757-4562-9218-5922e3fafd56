#!/bin/bash

# Source the selfsteal generator
source "$(dirname "${BASH_SOURCE[0]}")/../../lib/generate-selfsteal.sh"

# Create directory and generate selfsteal site
create_static_site() {
  local directory="$1"

  mkdir -p "$directory/html"

  # Change to the html directory to generate files there
  (
    cd "$directory/html"
    generate_selfsteal_form
    # Rename login.html to index.html for consistency
    if [[ -f "login.html" ]]; then
      mv "login.html" "index.html"
    fi
  ) >/dev/null 2>&1 &

  download_pid=$!
  spinner !$download_pid "$(t spinner_downloading_static_files)"
}
